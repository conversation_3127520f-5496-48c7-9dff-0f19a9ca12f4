// app/blog/[slug]/page.js

import { notFound } from "next/navigation";
import Image from "next/image";
import {Link} from '@/i18n/navigation';;
import { ArrowLeft, Calendar, User, Tag } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { getBlogBySlug } from "@/app/actions/server/blog";
import SocialShare from "@/components/layout/ShareSocial";
import { getTranslations } from "next-intl/server";

export async function generateMetadata({ params }) {
  const t = await getTranslations("NewsPage");
  const slug = params.slug;
  const response = await getBlogBySlug(slug);

  if (!response || !response.data) {
    return {
      title: t('notFoundTitle'),
      description: t('notFoundDescription'),
    };
  }

  const post = response.data;

  return {
    title: post.title,
    description: post.content.substring(0, 160),
    openGraph: {
      images: post.featuredImage ? [post.featuredImage] : [],
    },
  };
}

export default async function BlogPostDetail({ params }) {
  const t = await getTranslations("NewsPage");
  const locale = params.locale;

  const slug = params.slug;
  const response = await getBlogBySlug(slug);

  if (!response.data) {
    notFound();
  }

  const post = response.data;

  const formatDate = (dateString) => {
    if (!dateString) return "";
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString(locale, {
        year: "numeric",
        month: "long",
        day: "numeric",
        });
    } catch(error){
        console.error("Date format error:", error);
        return dateString;
    }
  };

  const renderContent = (content) => {
    if (/<[a-z][\s\S]*>/i.test(content)) {
      return (
         <div
           className="prose prose-lg max-w-none"
           dangerouslySetInnerHTML={{ __html: content }}
         />
       );
    } else {
       return (
          <div className="prose prose-lg max-w-none">
             {content.split("\n").map((paragraph, index) =>
               paragraph.trim() ? <p key={index}>{paragraph}</p> : null
             )}
           </div>
        );
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Button variant="outline" asChild>
            <Link href="/tin-tuc" className="flex items-center gap-2">
              <ArrowLeft size={16} />
              {t("backButton")}
            </Link>
          </Button>
        </div>

        <h1 className="text-3xl md:text-4xl font-bold mb-4">{post.title}</h1>

        <div className="flex flex-wrap gap-4 text-muted-foreground mb-6">
          {post.authorName && (
            <div className="flex items-center gap-1">
              <User size={16} />
              <span>{post.authorName}</span>
            </div>
          )}

          <div className="flex items-center gap-1">
            <Calendar size={16} />
            <span>{formatDate(post.publishedAt)}</span>
          </div>          
        </div>

        {post.featuredImage && (
          <div className="relative w-full h-96 mb-8 rounded-lg overflow-hidden">
            <Image
              src={post.featuredImage}
              alt={post.title}
              fill
              className="object-cover"
              priority
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 896px, 896px"
            />
          </div>
        )}

        {post.tags && (
          <div className="mb-6">
            <div className="flex items-center gap-2 mb-2">
              <Tag size={16} />
              <span className="font-medium">{t("tagsLabel")}</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {post.tags.split(",").map((tag) => (
                <Badge rounded="full" key={tag} variant="secondary">
                  {tag.trim()}
                </Badge>
              ))}
            </div>
          </div>
        )}

        <Separator className="my-6" />

        {renderContent(post.content)}

        <Separator className="my-8" />

        <SocialShare title={post.title} url={`/tin-tuc/${slug}`} />

        <Separator className="my-8" />

        <div className="mt-8">
          <h2 className="text-2xl font-bold mb-4">{t("commentsTitle")}</h2>
          {post.blogComments && post.blogComments.length > 0 ? (
            <div className="space-y-4">
              {post.blogComments.map((comment) => (
                <div key={comment.id} className="p-4 border rounded-lg bg-gray-50">
                  <div className="font-medium mb-1">{comment.authorName}</div>
                  <div className="text-sm text-muted-foreground mb-2">
                    {formatDate(comment.createdAt)}
                  </div>
                  <p className="text-gray-800">{comment.content}</p>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-muted-foreground">{t("noComments")}</p>
          )}
        </div>
      </div>
    </div>
  );
}
