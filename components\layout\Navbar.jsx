import { Link } from "@/i18n/navigation";
import Image from "next/image";
import { getTranslations } from "next-intl/server";
import { ClientNavbar } from "./ClientNavbar";

// Modify NavBarData to use translation keys
const NavBarData = [
  { id: 1, url: "/", nameKey: "Navbar.linkHome" }, // Assuming root is home
  { id: 2, url: "/?postType=sell", nameKey: "Navbar.linkBuy" }, // Updated URL for Buy
  { id: 3, url: "/?postType=rent", nameKey: "Navbar.linkRent" }, // Updated URL for Rent
  { id: 4, url: "/tin-tuc", nameKey: "Navbar.linkNews" },
  { id: 5, url: "/bieu-phi", nameKey: "Navbar.linkPricing" },
  { id: 6, url: "/gioi-thieu", nameKey: "Navbar.linkAbout" },
  { id: 7, url: "/lien-he", nameKey: "Navbar.linkContact" },
];

// NavLinks now accepts and uses the translation function
const NavLinks = ({ t }) => (
  <>
    {NavBarData.map((item) => (
      <Link key={item.id} href={item.url} className="font-montserrat text-sm font-medium py-2 hover:text-teal-600 transition-colors block">
        {t(item.nameKey)} {/* Use translation key */}
      </Link>
    ))}
  </>
);

export default async function Navbar() {
  const t = await getTranslations(); // Get translation function
  
  return (
    <nav className="flex sticky top-0 z-50 w-full items-center border-b bg-background">
      <div className="mx-auto flex justify-between items-center w-full">
        <div className="flex items-center space-x-12">
          <Link href="/" className="flex items-center space-x-3 pr-6">
            <Image
              src="/yezhome_logo.png"
              alt={t("Navbar.logoAlt")} // Use translated alt text
              width={150}
              height={150}
              className="p-3"
              priority={true}
              quality={90}
            />
          </Link>
          {/* Desktop Navigation */}
          <div className="hidden md:flex space-x-9 items-center pt-2">
            <NavLinks t={t} /> {/* Pass translation function */}
          </div>
        </div>
        <ClientNavbar/>
      </div>
    </nav>
  );
}
